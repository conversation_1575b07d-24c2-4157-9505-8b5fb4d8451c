#!/usr/bin/env python3
"""
Test script to verify Instagram account connection fix
"""

import sys
import os
sys.path.append('integrations')

def test_instagram_connect():
    """Test Instagram account connection directly"""
    try:
        print("Testing Instagram account connection fix...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        print("✅ Instagram API initialized successfully")
        print(f"Base URL: {instagram.unipile_client.base_url}")
        
        # Test connection with dummy credentials (will fail but should not get 404)
        print("Testing connect_account method...")
        result = instagram.connect_account("test_user", "test_password")
        
        print(f"Connection result: {result}")
        
        # Check if we get a proper error response instead of 404
        if "error" in result:
            error_msg = result.get("error", "")
            if "404" in str(error_msg) or "Not Found" in str(error_msg):
                print("❌ Still getting 404 error - endpoint issue not fixed")
                return False
            else:
                print("✅ No 404 error - endpoint is working correctly")
                print(f"Expected error (invalid credentials): {error_msg}")
                return True
        else:
            print("✅ Connection successful (unexpected but good)")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unipile_accounts_endpoint():
    """Test UnipileAPI accounts endpoint directly"""
    try:
        print("\nTesting UnipileAPI accounts endpoint directly...")
        
        from unipile_api import UnipileAPI
        
        # Test with Instagram API key
        api_key = "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
        unipile = UnipileAPI(api_key=api_key)
        
        print(f"Base URL: {unipile.base_url}")
        
        # Test the accounts endpoint that Instagram connect_account uses
        print("Testing POST to api/v1/accounts endpoint...")
        test_data = {
            "provider": "INSTAGRAM",
            "username": "test_user",
            "password": "test_password"
        }
        
        result = unipile._make_request("POST", "api/v1/accounts", test_data)
        
        print(f"Accounts endpoint result: {result}")
        
        if "error" in result:
            error_msg = result.get("error", "")
            if "404" in str(error_msg) or "Not Found" in str(error_msg):
                print("❌ 404 error on accounts endpoint")
                return False
            else:
                print("✅ Accounts endpoint working (expected error for invalid credentials)")
                return True
        else:
            print("✅ Accounts endpoint working")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Testing Instagram account connection endpoint fix...\n")
    
    # Test UnipileAPI directly first
    if test_unipile_accounts_endpoint():
        print("\n" + "="*50)
        # Test Instagram API
        test_instagram_connect()
    
    print("\n🏁 Test completed.")
