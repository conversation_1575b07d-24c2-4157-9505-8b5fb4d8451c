#!/usr/bin/env python3
"""
Test script for Facebook Messenger integration
Tests the FacebookMessaging class functionality
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_facebook_integration():
    """Test Facebook integration functionality"""
    print("🧪 Testing Facebook Messenger Integration")
    print("=" * 50)
    
    try:
        # Import Facebook API
        from facebook_integration.facebook_api import FacebookMessaging
        print("✅ Facebook API imported successfully")
        
        # Initialize Facebook client
        facebook = FacebookMessaging()
        print("✅ Facebook client initialized")
        
        # Test configuration status
        print("\n📊 Configuration Status:")
        status = facebook.get_configuration_status()
        for key, value in status.items():
            icon = "✅" if value else "❌"
            print(f"  {icon} {key}: {value}")
        
        # Test OAuth configuration
        print(f"\n🔐 OAuth configured: {'✅' if facebook.is_oauth_configured() else '❌'}")
        print(f"📄 Page configured: {'✅' if facebook.is_configured() else '❌'}")
        
        # Test config file loading
        print(f"\n📁 Config file path: {facebook.config_path}")
        print(f"📁 Config file exists: {'✅' if os.path.exists(facebook.config_path) else '❌'}")
        
        if facebook.config:
            print("📋 Current configuration keys:")
            for key in facebook.config.keys():
                has_value = bool(facebook.config.get(key))
                icon = "✅" if has_value else "❌"
                print(f"  {icon} {key}")
        
        # Test connection (will fail without credentials, but should not crash)
        print(f"\n🔗 Testing connection...")
        try:
            connection_result = facebook.test_connection()
            if connection_result.get("success"):
                print("✅ Connection test successful!")
                print(f"   Page: {connection_result.get('page_name')}")
                print(f"   ID: {connection_result.get('page_id')}")
            else:
                print(f"❌ Connection test failed (expected): {connection_result.get('error')}")
        except Exception as e:
            print(f"❌ Connection test error: {e}")
        
        # Test OAuth flow (without actual credentials)
        print(f"\n🔐 Testing OAuth flow...")
        try:
            if facebook.is_oauth_configured():
                oauth_result = facebook.start_oauth_flow()
                if oauth_result.get("success"):
                    print("✅ OAuth flow started successfully")
                    print(f"   Auth URL generated: {len(oauth_result.get('auth_url', ''))} characters")
                else:
                    print(f"❌ OAuth flow failed: {oauth_result.get('error')}")
            else:
                print("❌ OAuth not configured (app_id and app_secret needed)")
        except Exception as e:
            print(f"❌ OAuth flow error: {e}")
        
        print(f"\n✅ Facebook integration test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_config_update():
    """Test configuration update functionality"""
    print(f"\n🔧 Testing configuration update...")
    
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        
        # Test config update
        test_config = {
            "app_id": "test_app_id",
            "app_secret": "test_app_secret",
            "page_id": "test_page_id"
        }
        
        result = facebook.update_config(**test_config)
        if result.get("success"):
            print("✅ Configuration update successful")
            
            # Verify the update
            for key, value in test_config.items():
                if facebook.config.get(key) == value:
                    print(f"  ✅ {key} updated correctly")
                else:
                    print(f"  ❌ {key} update failed")
        else:
            print(f"❌ Configuration update failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Config update test error: {e}")

if __name__ == "__main__":
    success = test_facebook_integration()
    test_config_update()
    
    if success:
        print(f"\n🎉 All tests passed! Facebook integration is ready.")
        print(f"\n💡 Next steps:")
        print(f"1. Get Facebook App ID and App Secret from developers.facebook.com")
        print(f"2. Update config.json with your credentials")
        print(f"3. Start the server: python run_server.py")
        print(f"4. Visit http://localhost:8000/facebook/facebook_auth.html")
        print(f"5. Use OAuth flow to connect your Facebook Page")
    else:
        print(f"\n❌ Tests failed. Please check the error messages above.")
