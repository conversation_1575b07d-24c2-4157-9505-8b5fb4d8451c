#!/usr/bin/env python3
"""
Test script for connected Instagram account
"""

import sys
import os
sys.path.append('integrations')

def test_connected_instagram_account():
    """Test Instagram account info with connected account"""
    try:
        print("Testing connected Instagram account...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        print("✅ Instagram API initialized successfully")
        
        # Test connection status first
        print("Testing connection status...")
        status = instagram.get_connection_status()
        print(f"Connection status: {status}")
        
        # Test get_account_info method
        print("\nTesting get_account_info method...")
        result = instagram.get_account_info()
        
        print(f"Account info result: {result}")
        print(f"Result type: {type(result)}")
        
        if isinstance(result, dict):
            print(f"Result keys: {list(result.keys())}")
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
                return False
            elif "success" in result and result["success"]:
                print("✅ Account info retrieved successfully!")
                print(f"   Account ID: {result.get('id', 'N/A')}")
                print(f"   Username: {result.get('username', 'N/A')}")
                print(f"   Name: {result.get('name', 'N/A')}")
                return True
            else:
                print(f"⚠️  Unexpected result format: {result}")
                return False
        else:
            print(f"❌ Unexpected result type: {type(result)}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint_directly():
    """Test the API endpoint directly"""
    try:
        print("\nTesting API endpoint directly...")
        
        import requests
        import json
        
        response = requests.get('http://localhost:8000/api/instagram/account-info', timeout=10)
        print(f'API Response - Status Code: {response.status_code}')
        
        if response.status_code == 200:
            data = response.json()
            print(f'✅ Success Response: {json.dumps(data, indent=2)}')
            return True
        else:
            print(f'❌ Error Response: {response.text}')
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - server may not be running")
        return False
    except Exception as e:
        print(f"❌ API Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing connected Instagram account...\n")
    
    # Test the method directly first
    method_success = test_connected_instagram_account()
    
    if method_success:
        print("\n" + "="*50)
        # Test the API endpoint
        api_success = test_api_endpoint_directly()
        
        if not api_success:
            print("\n⚠️  The Instagram method works but API endpoint doesn't.")
            print("   This suggests the server needs to be restarted to pick up the fixes.")
    else:
        print("\n❌ Instagram account method is not working properly.")
        print("   Please check if the account is properly connected.")
    
    print("\n🏁 Test completed.")
