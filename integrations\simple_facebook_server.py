#!/usr/bin/env python3
"""
Simple test server for Facebook integration
Tests Facebook API endpoints without the full unified messaging system
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field
from datetime import datetime
import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Initialize FastAPI app
app = FastAPI(title="Facebook Integration Test Server")

# Mount Facebook static files
try:
    facebook_dir = os.path.join(os.path.dirname(__file__), "facebook_integration")
    if os.path.exists(facebook_dir):
        app.mount("/facebook", StaticFiles(directory=facebook_dir, html=True), name="facebook_static")
        print(f"✅ Mounted Facebook static files from {facebook_dir}")
except Exception as e:
    print(f"⚠️ Static file mounting error: {e}")

# Pydantic models
class FacebookConfigRequest(BaseModel):
    page_access_token: str = Field(..., description="Facebook Page Access Token")
    app_secret: str = Field(..., description="Facebook App Secret")
    page_id: str = Field(..., description="Facebook Page ID")

class FacebookGreetingRequest(BaseModel):
    greeting: str = Field(..., description="Greeting text for the Facebook page")

class FacebookTestMessageRequest(BaseModel):
    recipient_id: str = Field(..., description="Facebook Page-Scoped ID (PSID) of the recipient")
    message: str = Field(..., description="Test message to send")

# Global storage for OAuth states
oauth_states = {}

@app.get("/")
async def root():
    """Root endpoint"""
    return HTMLResponse(content="""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Facebook Integration Test</title>
        <style>body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }</style>
    </head>
    <body>
        <h1>🧪 Facebook Integration Test Server</h1>
        <p><a href="/facebook/facebook_auth.html">📘 Facebook Setup Page</a></p>
        <p><a href="/docs">📖 API Documentation</a></p>
    </body>
    </html>
    """)

@app.get("/api/facebook/status")
async def get_facebook_status():
    """Get Facebook connection status"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        
        if not facebook.page_access_token:
            return {
                "success": True,
                "connected": False,
                "message": "Facebook not configured",
                "timestamp": datetime.now().isoformat()
            }
        
        # Test connection
        test_result = facebook.test_connection()
        
        if test_result.get("success"):
            return {
                "success": True,
                "connected": True,
                "page_name": test_result.get("page_name", "Unknown"),
                "page_id": facebook.page_id,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": True,
                "connected": False,
                "error": test_result.get("error", "Connection test failed"),
                "timestamp": datetime.now().isoformat()
            }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/facebook/config")
async def update_facebook_config(request: FacebookConfigRequest):
    """Update Facebook configuration"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        
        # Update configuration
        facebook.config.update({
            "page_access_token": request.page_access_token,
            "app_secret": request.app_secret,
            "page_id": request.page_id
        })
        
        facebook._save_config()
        
        return {
            "success": True,
            "message": "Facebook configuration updated successfully",
            "timestamp": datetime.now().isoformat()
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/test")
async def test_facebook_connection():
    """Test Facebook connection"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        result = facebook.test_connection()
        
        if result.get("success"):
            return {
                "success": True,
                "page_name": result.get("page_name"),
                "page_id": result.get("page_id"),
                "category": result.get("category"),
                "message": result.get("message"),
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": result.get("error"),
                "timestamp": datetime.now().isoformat()
            }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/facebook/oauth/start")
async def start_facebook_oauth():
    """Start Facebook OAuth flow"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        result = facebook.start_oauth_flow()
        
        if result.get("success"):
            return {
                "success": True,
                "auth_url": result["auth_url"],
                "state": result["state"],
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=result.get("error", "Failed to start OAuth flow"))
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/oauth/callback")
async def facebook_oauth_callback(code: str, state: str):
    """Handle Facebook OAuth callback"""
    try:
        from facebook_integration.facebook_api import FacebookMessaging
        facebook = FacebookMessaging()
        result = facebook.handle_oauth_callback(code, state)
        
        if result.get("success"):
            # Store completion status for polling
            oauth_states[state] = {
                "completed": True,
                "success": True,
                "page_name": result.get("page_name", "Connected"),
                "page_id": result.get("page_id", "N/A"),
                "timestamp": datetime.now().isoformat()
            }
            
            return HTMLResponse(content=f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>Facebook Authentication Success</title>
                <style>
                    body {{ font-family: Arial, sans-serif; text-align: center; padding: 50px; }}
                    .success {{ color: #28a745; }}
                </style>
            </head>
            <body>
                <h1 class="success">✅ Facebook Authentication Successful!</h1>
                <p><strong>Page:</strong> {result.get('page_name', 'Connected')}</p>
                <p><strong>Page ID:</strong> {result.get('page_id', 'N/A')}</p>
                <p>You can now close this window and return to the setup page.</p>
                <script>setTimeout(() => {{ window.close(); }}, 3000);</script>
            </body>
            </html>
            """)
        else:
            oauth_states[state] = {
                "completed": True,
                "success": False,
                "error": result.get("error", "OAuth failed"),
                "timestamp": datetime.now().isoformat()
            }
            raise HTTPException(status_code=400, detail=result.get("error", "OAuth callback failed"))
    
    except HTTPException:
        raise
    except Exception as e:
        if 'state' in locals():
            oauth_states[state] = {
                "completed": True,
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/facebook/oauth/status")
async def get_facebook_oauth_status(state: str):
    """Get Facebook OAuth status"""
    try:
        if state in oauth_states:
            return oauth_states[state]
        else:
            return {
                "completed": False,
                "message": "OAuth in progress",
                "timestamp": datetime.now().isoformat()
            }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting Facebook Integration Test Server...")
    print("📍 Server will be available at: http://localhost:8001")
    print("📘 Facebook setup: http://localhost:8001/facebook/facebook_auth.html")
    uvicorn.run(app, host="127.0.0.1", port=8001, log_level="info")
