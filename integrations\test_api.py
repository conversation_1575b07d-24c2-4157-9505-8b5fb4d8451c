#!/usr/bin/env python3
"""
Test script for Telegram API endpoints
"""

import requests
import json

def test_telegram_api():
    """Test Telegram API endpoints"""
    print("🔧 Testing Telegram API endpoints...")
    
    try:
        # Test authentication endpoint (normal)
        print("\n📱 Testing QR code generation (normal)...")
        response = requests.post('http://localhost:8000/api/telegram/authenticate', timeout=10)
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint working!")
            print(f"Success: {data.get('success')}")
            print(f"Data keys: {list(data.get('data', {}).keys())}")
            if data.get('success') and data.get('data', {}).get('qr_code'):
                qr_length = len(data['data']['qr_code'])
                print(f"QR Code Length: {qr_length} characters")
                print("✅ QR code generated successfully!")
            else:
                print(f"Connected: {data.get('data', {}).get('connected')}")
                print(f"Message: {data.get('data', {}).get('message')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")

    except Exception as e:
        print(f"❌ Error: {e}")

    try:
        # Test authentication endpoint (force new)
        print("\n📱 Testing QR code generation (force new)...")
        response = requests.post('http://localhost:8000/api/telegram/authenticate?force=true', timeout=10)
        print(f"Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Force new API endpoint working!")
            print(f"Success: {data.get('success')}")
            if data.get('success') and data.get('data', {}).get('qr_code'):
                qr_length = len(data['data']['qr_code'])
                print(f"QR Code Length: {qr_length} characters")
                print("✅ New QR code generated successfully!")
            else:
                print(f"❌ No QR code in response")
                print(f"Data: {data.get('data')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")

    except Exception as e:
        print(f"❌ Error: {e}")

    try:
        # Test status endpoint
        print("\n📡 Testing status endpoint...")
        response = requests.get('http://localhost:8000/api/telegram/status', timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Status endpoint working!")
            print(f"Connected: {data.get('data', {}).get('connected')}")
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_telegram_api()
