#!/usr/bin/env python3
"""
Test the newly connected Instagram account
"""

import sys
import os
sys.path.append('integrations')

def test_new_instagram_account():
    """Test the newly connected Instagram account"""
    try:
        print("🔍 Testing newly connected Instagram account...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        from unipile_api import UnipileAPI
        
        # The new account ID
        new_account_id = "BVUdNoAGRa6Z_u3OrWjO2w"
        print(f"Looking for new account ID: {new_account_id}")
        
        print("\n1. Testing Instagram API...")
        instagram = InstagramMessaging()
        print(f"   API Key: {instagram.unipile_client.api_key[:20]}...")
        
        # Force refresh and get accounts
        accounts = instagram.unipile_client.get_accounts(force_refresh=True)
        
        if "error" in accounts:
            print(f"   ❌ Error getting accounts: {accounts['error']}")
            return False
        
        print(f"   Total accounts: {len(accounts.get('items', []))}")
        
        # Look for the new Instagram account
        instagram_found = False
        for account in accounts.get('items', []):
            account_id = account.get('id')
            account_type = account.get('type', 'N/A')
            account_name = account.get('name', 'N/A')
            
            print(f"   - ID: {account_id}, Type: {account_type}, Name: {account_name}")
            
            if account_id == new_account_id:
                print(f"   🎯 FOUND NEW INSTAGRAM ACCOUNT!")
                print(f"      Type: {account_type}")
                print(f"      Name: {account_name}")
                instagram_found = True
            elif account_type.upper() == "INSTAGRAM":
                print(f"   📱 Found Instagram account: {account_id}")
                instagram_found = True
        
        if not instagram_found:
            print(f"   ❌ New Instagram account not found yet")
            return False
        
        print("\n2. Testing Instagram connection status...")
        status = instagram.get_connection_status()
        print(f"   Connection status: {status}")
        
        if status.get('unipile', {}).get('connected', False):
            print("   ✅ Instagram shows as connected!")
        else:
            print("   ⚠️  Instagram not showing as connected yet")
        
        print("\n3. Testing get_account_info method...")
        account_info = instagram.get_account_info()
        print(f"   Account info result: {account_info}")
        
        if "error" in account_info:
            print(f"   ❌ Error: {account_info['error']}")
            return False
        elif account_info.get("success", False):
            print("   ✅ Account info retrieved successfully!")
            print(f"      Account ID: {account_info.get('id', 'N/A')}")
            print(f"      Username: {account_info.get('username', 'N/A')}")
            print(f"      Name: {account_info.get('name', 'N/A')}")
            return True
        else:
            print(f"   ⚠️  Unexpected account info format: {account_info}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_endpoint():
    """Test the API endpoint with the new account"""
    try:
        print("\n4. Testing API endpoint...")
        
        import requests
        import json
        
        response = requests.get('http://localhost:8000/api/instagram/account-info', timeout=10)
        print(f"   Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ API endpoint working!")
            print(f"   Response: {json.dumps(data, indent=4)}")
            return True
        else:
            print(f"   Response: {response.text}")
            
            # Check if it's the old error format or new format
            try:
                error_data = response.json()
                if "success" in error_data and error_data["success"] == False:
                    print("   ✅ New error format working (structured response)")
                    print("   This means the API fix is working, just no account connected yet")
                    return True
                else:
                    print("   ❌ Unexpected response format")
                    return False
            except:
                print("   ❌ Non-JSON response")
                return False
            
    except requests.exceptions.ConnectionError:
        print("   ❌ Connection error - server may not be running")
        return False
    except Exception as e:
        print(f"   ❌ API Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Testing newly connected Instagram account...\n")
    
    # Test the Instagram method
    method_success = test_new_instagram_account()
    
    # Test the API endpoint regardless
    api_success = test_api_endpoint()
    
    print(f"\n📊 RESULTS:")
    print(f"   Instagram Method: {'✅ Working' if method_success else '❌ Not working'}")
    print(f"   API Endpoint: {'✅ Working' if api_success else '❌ Not working'}")
    
    if method_success and api_success:
        print(f"\n🎉 SUCCESS! Your Instagram account is properly connected!")
        print(f"   The account info button should now work correctly.")
    elif method_success and not api_success:
        print(f"\n⚠️  Instagram method works but API endpoint doesn't.")
        print(f"   The server may need to be restarted to pick up the fixes.")
    elif not method_success and api_success:
        print(f"\n⚠️  API endpoint is fixed but Instagram account not detected yet.")
        print(f"   Wait a few minutes and try again, or reconnect the account.")
    else:
        print(f"\n❌ Both method and API endpoint have issues.")
        print(f"   Please check the connection and try reconnecting.")
    
    print("\n🏁 Test completed.")
