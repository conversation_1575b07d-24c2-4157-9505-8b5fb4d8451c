#!/usr/bin/env python3
"""
Test script for API endpoints
"""

import requests
import json
import time

def test_single_message():
    """Test single message endpoint"""
    print("Testing single message endpoint...")
    try:
        response = requests.post(
            'http://localhost:8000/api/messaging/send',
            headers={'Content-Type': 'application/json'},
            json={
                'platform': 'whatsapp',
                'recipient': '+2348033983592',
                'message': 'Test single message from API'
            },
            timeout=10
        )
        print(f"Single message - Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        print(f"Single message error: {e}")
        return False

def test_simple_bulk():
    """Test simple bulk endpoint"""
    print("\nTesting simple bulk endpoint...")
    try:
        response = requests.post(
            'http://localhost:8000/api/messaging/bulk-simple',
            headers={'Content-Type': 'application/json'},
            json={
                'platform': 'whatsapp',
                'recipients': ['+2348033983592'],
                'message': 'Test simple bulk message',
                'delay': 0.5
            },
            timeout=15
        )
        print(f"Simple bulk - Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        print(f"Simple bulk error: {e}")
        return False

def test_whatsapp_bulk():
    """Test WhatsApp specific bulk endpoint"""
    print("\nTesting WhatsApp bulk endpoint...")
    try:
        response = requests.post(
            'http://localhost:8000/api/whatsapp/bulk',
            headers={'Content-Type': 'application/json'},
            json={
                'recipients': ['+2348033983592'],
                'message': 'Test WhatsApp bulk message',
                'delay': 0.5
            },
            timeout=15
        )
        print(f"WhatsApp bulk - Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return True
    except Exception as e:
        print(f"WhatsApp bulk error: {e}")
        return False

if __name__ == "__main__":
    print("Testing API endpoints...")
    
    # Test single message first
    if test_single_message():
        time.sleep(2)
        
        # Test simple bulk
        if test_simple_bulk():
            time.sleep(2)
            
            # Test WhatsApp bulk
            test_whatsapp_bulk()
    
    print("\nTest completed.")
