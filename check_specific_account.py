#!/usr/bin/env python3
"""
Check for specific Instagram account ID
"""

import sys
import os
sys.path.append('integrations')

def check_specific_account_id():
    """Check if the specific Instagram account ID exists"""
    try:
        print("🔍 Checking for specific Instagram account ID...")
        
        from unipile_api import UnipileAPI
        
        # The account ID you got when connecting
        target_account_id = "qFyUcKAVRQGaTtNFs5n0aw"
        print(f"Looking for account ID: {target_account_id}")
        
        # Test both API keys
        api_keys = [
            ("Instagram API Key", "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="),
            ("WhatsApp API Key", "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
        ]
        
        for key_name, api_key in api_keys:
            print(f"\n📋 Testing {key_name}...")
            print(f"   API Key: {api_key[:20]}...")
            
            unipile = UnipileAPI(api_key=api_key)
            
            # Get all accounts
            accounts = unipile.get_accounts(force_refresh=True)
            
            if "error" in accounts:
                print(f"   ❌ Error: {accounts['error']}")
                continue
            
            print(f"   Total accounts: {len(accounts.get('items', []))}")
            
            # Check each account
            found_target = False
            for account in accounts.get('items', []):
                account_id = account.get('id')
                account_type = account.get('type', 'N/A')
                account_name = account.get('name', 'N/A')
                
                print(f"   - ID: {account_id}, Type: {account_type}, Name: {account_name}")
                
                if account_id == target_account_id:
                    print(f"   🎯 FOUND TARGET ACCOUNT!")
                    print(f"      Type: {account_type}")
                    print(f"      Name: {account_name}")
                    found_target = True
            
            if not found_target:
                print(f"   ❌ Target account {target_account_id} not found")
        
        print(f"\n🔍 Attempting direct account lookup...")
        
        # Try to get the specific account directly (if Unipile supports it)
        for key_name, api_key in api_keys:
            try:
                unipile = UnipileAPI(api_key=api_key)
                
                # Try to make a direct request to get the specific account
                result = unipile._make_request("GET", f"api/v1/accounts/{target_account_id}")
                
                print(f"\n📋 Direct lookup with {key_name}:")
                print(f"   Result: {result}")
                
                if "error" not in result:
                    print(f"   ✅ Account found via direct lookup!")
                    return True
                    
            except Exception as e:
                print(f"   ❌ Direct lookup failed: {e}")
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """Suggest solutions for the missing account"""
    print("\n💡 POSSIBLE SOLUTIONS:")
    print("\n1. 🔄 Try reconnecting the Instagram account:")
    print("   - Go to the Instagram authentication page")
    print("   - Enter your Instagram credentials again")
    print("   - Make sure you're using the correct API key")
    
    print("\n2. 🕐 Wait and retry:")
    print("   - Sometimes there's a delay in Unipile's system")
    print("   - Wait 5-10 minutes and try again")
    
    print("\n3. 🔑 Check API key configuration:")
    print("   - Verify you're using the correct Unipile API key")
    print("   - Make sure the account was connected to the same workspace")
    
    print("\n4. 📞 Contact Unipile support:")
    print("   - If the account ID was generated but doesn't appear")
    print("   - This might be a Unipile platform issue")
    
    print("\n5. 🔍 Check Unipile dashboard:")
    print("   - Log into https://app.unipile.com/dashboard")
    print("   - Verify if the Instagram account appears there")
    print("   - Check which API key it's associated with")

if __name__ == "__main__":
    print("🔧 Checking for specific Instagram account...\n")
    
    found = check_specific_account_id()
    
    if not found:
        suggest_solutions()
    
    print("\n🏁 Check completed.")
