#!/usr/bin/env python3
"""
Test script to verify Instagram API URL fix
"""

import sys
import os
sys.path.append('integrations')

def test_instagram_api():
    """Test Instagram API initialization and connection"""
    try:
        print("Testing Instagram API URL fix...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        print("✅ Instagram API initialized successfully")
        print(f"Base URL: {instagram.unipile_client.base_url}")
        
        # Test connection status
        print("Testing connection status...")
        status = instagram.get_connection_status()
        
        if "error" in str(status).lower():
            print(f"❌ Connection test failed: {status}")
        else:
            print(f"✅ Connection test successful: {status}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_unipile_api_directly():
    """Test UnipileAPI directly"""
    try:
        print("\nTesting UnipileAPI directly...")
        
        from unipile_api import UnipileAPI
        
        # Test with Instagram API key
        api_key = "iGJsHDIR.sr6Pg30nB1cKUPC6eDY8tzVx+Opu+t+c6wQiKAEz2Ek="
        unipile = UnipileAPI(api_key=api_key)
        
        print(f"Base URL: {unipile.base_url}")
        
        # Test accounts endpoint
        print("Testing accounts endpoint...")
        accounts = unipile.get_accounts()
        
        if "error" in accounts:
            print(f"❌ Accounts test failed: {accounts['error']}")
        else:
            print(f"✅ Accounts test successful: Found {len(accounts.get('items', []))} accounts")
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 Testing Instagram API URL duplication fix...\n")
    
    # Test UnipileAPI directly first
    if test_unipile_api_directly():
        print("\n" + "="*50)
        # Test Instagram API
        test_instagram_api()
    
    print("\n🏁 Test completed.")
