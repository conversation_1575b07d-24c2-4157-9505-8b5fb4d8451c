<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Integration - QR Code Authentication</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0088cc 0%, #005577 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #0088cc, #0066aa);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .telegram-icon {
            font-size: 1.5em;
        }

        .content {
            padding: 40px;
        }

        .auth-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #0088cc;
        }

        .auth-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #1565c0;
        }

        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            border: 2px solid #e1e5e9;
            margin: 20px 0;
            min-height: 400px;
            justify-content: center;
        }

        .qr-code {
            width: 256px;
            height: 256px;
            border: 2px solid #0088cc;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            margin-bottom: 20px;
        }

        .qr-placeholder {
            color: #6c757d;
            text-align: center;
            font-size: 1.1em;
        }

        .qr-instructions {
            text-align: center;
            color: #333;
            max-width: 400px;
        }

        .qr-instructions h3 {
            color: #0088cc;
            margin-bottom: 10px;
        }

        .qr-instructions ol {
            text-align: left;
            margin: 15px 0;
        }

        .qr-instructions li {
            margin: 8px 0;
            padding-left: 5px;
        }

        .btn {
            background: linear-gradient(135deg, #0088cc, #0066aa);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn:hover {
            background: linear-gradient(135deg, #0066aa, #004488);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 136, 204, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #5a6268, #495057);
        }

        .status-indicator {
            padding: 15px 20px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0088cc;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #0088cc;
        }

        .result {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }

        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .result.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .qr-code {
                width: 200px;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <span class="telegram-icon">📱</span>
                Telegram Integration
            </h1>
            <p>Connect your Telegram account using QR code authentication</p>
        </div>

        <div class="content">
            <!-- QR Code Authentication Section -->
            <div class="auth-section">
                <h2>📱 QR Code Authentication</h2>
                <div class="info">
                    <strong>Scan the QR code with your Telegram app to connect your account.</strong>
                    <br>This method provides secure access to your Telegram account for messaging.
                </div>

                <div class="qr-container" id="qrContainer">
                    <div class="qr-code" id="qrCode">
                        <div class="qr-placeholder">
                            <div style="font-size: 3em; margin-bottom: 10px;">📱</div>
                            <div>Click "Generate QR Code" to start</div>
                        </div>
                    </div>
                    <div class="qr-instructions">
                        <h3>How to connect:</h3>
                        <ol>
                            <li>Open Telegram on your phone</li>
                            <li>Go to Settings → Devices → Link Desktop Device</li>
                            <li>Scan the QR code above</li>
                            <li>Your account will be connected automatically</li>
                        </ol>
                    </div>
                </div>

                <div style="text-align: center;">
                    <button class="btn" onclick="generateQRCode()">📱 Generate QR Code</button>
                    <button class="btn btn-secondary" onclick="refreshQRCode()">🔄 Refresh QR Code</button>
                </div>

                <div id="qrResult"></div>
            </div>

            <!-- Connection Status -->
            <div class="auth-section">
                <h2>🔗 Connection Status</h2>
                <div id="connectionStatus" class="status-indicator status-loading">
                    <span class="loading-spinner"></span> Checking connection status...
                </div>
                <button class="btn" onclick="checkConnectionStatus()">Refresh Status</button>
            </div>

            <!-- Testing Section -->
            <div class="auth-section">
                <h2>🧪 Test Messaging</h2>
                <div class="info">
                    <strong>Test your Telegram connection by sending a message.</strong>
                </div>

                <div class="form-group">
                    <label for="testRecipient">Recipient (Username or Chat ID):</label>
                    <input type="text" id="testRecipient" placeholder="@username or chat_id">
                </div>

                <div class="form-group">
                    <label for="testMessage">Test Message:</label>
                    <textarea id="testMessage" rows="3" placeholder="Enter your test message here...">Hello! This is a test message from your Telegram integration.</textarea>
                </div>

                <button class="btn" onclick="sendTestMessage()">📤 Send Test Message</button>
                <div id="testResult"></div>
            </div>

            <!-- Bulk Messaging Section -->
            <div class="auth-section">
                <h2>📢 Bulk Messaging</h2>
                <div class="info">
                    <strong>Send messages to multiple recipients at once.</strong>
                </div>

                <div class="form-group">
                    <label for="bulkRecipients">Recipients (one per line):</label>
                    <textarea id="bulkRecipients" rows="4" placeholder="@username1&#10;@username2&#10;chat_id_3"></textarea>
                </div>

                <div class="form-group">
                    <label for="bulkMessage">Bulk Message:</label>
                    <textarea id="bulkMessage" rows="3" placeholder="Enter your bulk message here...">Hello! This is a bulk message from your Telegram integration.</textarea>
                </div>

                <button class="btn" onclick="sendBulkMessages()">📤 Send Bulk Messages</button>
                <div id="bulkResult"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let qrCheckInterval = null;

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        function showLoading(elementId, message) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status-indicator status-loading"><span class="loading-spinner"></span> ${message}</div>`;
        }

        // QR Code Authentication Functions
        function generateQRCode() {
            showLoading('qrResult', 'Generating QR code...');

            fetch('/api/telegram/authenticate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.qr_code) {
                    // Display QR code
                    document.getElementById('qrCode').innerHTML =
                        `<img src="${data.data.qr_code}" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain;">`;

                    showResult('qrResult', '✅ QR code generated! Scan with your Telegram app to connect.', 'success');

                    // Start checking for connection
                    startQRConnectionCheck();
                } else {
                    const error = data.error || data.data?.error || 'Failed to generate QR code';
                    showResult('qrResult', `❌ ${error}`, 'error');
                }
            })
            .catch(error => {
                showResult('qrResult', `❌ Error: ${error.message}`, 'error');
            });
        }

        function refreshQRCode() {
            // Stop current checking
            if (qrCheckInterval) {
                clearInterval(qrCheckInterval);
                qrCheckInterval = null;
            }

            // Generate new QR code
            generateQRCode();
        }

        function startQRConnectionCheck() {
            // Clear any existing interval
            if (qrCheckInterval) {
                clearInterval(qrCheckInterval);
            }

            // Check connection status every 3 seconds
            qrCheckInterval = setInterval(() => {
                checkConnectionStatus(false); // Don't show loading for automatic checks
            }, 3000);
        }

        function checkConnectionStatus(showLoadingIndicator = true) {
            if (showLoadingIndicator) {
                updateConnectionStatus('loading', '<span class="loading-spinner"></span> Checking connection status...');
            }

            fetch('/api/telegram/status')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data) {
                    const status = data.data;

                    if (status.connected) {
                        // Connected successfully
                        const accountInfo = status.account_info || {};
                        const html = `✅ Connected: ${accountInfo.name || 'Telegram Account'} ${accountInfo.username ? `(@${accountInfo.username})` : ''}`;
                        updateConnectionStatus('connected', html);

                        // Stop QR checking
                        if (qrCheckInterval) {
                            clearInterval(qrCheckInterval);
                            qrCheckInterval = null;
                        }

                        showResult('qrResult', '🎉 Telegram account connected successfully!', 'success');
                    } else {
                        // Not connected
                        const html = status.qr_required
                            ? '❌ Not connected. Please scan QR code to authenticate.'
                            : '❌ Not connected. Please generate QR code first.';
                        updateConnectionStatus('disconnected', html);
                    }
                } else {
                    updateConnectionStatus('disconnected', '❌ Failed to get status: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                updateConnectionStatus('disconnected', '❌ Status check error: ' + error.message);
            });
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status-indicator status-${status}`;
            statusElement.innerHTML = message;
        }

        // Messaging Functions
        function sendTestMessage() {
            const recipient = document.getElementById('testRecipient').value.trim();
            const message = document.getElementById('testMessage').value.trim();

            if (!recipient || !message) {
                showResult('testResult', '❌ Please enter both recipient and message', 'error');
                return;
            }

            showLoading('testResult', 'Sending test message...');

            fetch('/api/messaging/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'telegram',
                    recipient: recipient,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult('testResult', `✅ Message sent successfully to ${recipient}!`, 'success');
                } else {
                    showResult('testResult', `❌ Failed to send message: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showResult('testResult', `❌ Error: ${error.message}`, 'error');
            });
        }

        function sendBulkMessages() {
            const recipients = document.getElementById('bulkRecipients').value.trim().split('\n').filter(r => r.trim());
            const message = document.getElementById('bulkMessage').value.trim();

            if (recipients.length === 0 || !message) {
                showResult('bulkResult', '❌ Please enter recipients and message', 'error');
                return;
            }

            showLoading('bulkResult', `Sending bulk messages to ${recipients.length} recipients...`);

            fetch('/api/messaging/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'telegram',
                    recipients: recipients,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const results = data.results || [];
                    const successful = results.filter(r => r.success).length;
                    const failed = results.length - successful;

                    showResult('bulkResult',
                        `✅ Bulk messaging completed: ${successful} sent, ${failed} failed`,
                        successful > 0 ? 'success' : 'error'
                    );
                } else {
                    showResult('bulkResult', `❌ Bulk messaging failed: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showResult('bulkResult', `❌ Error: ${error.message}`, 'error');
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkConnectionStatus();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (qrCheckInterval) {
                clearInterval(qrCheckInterval);
            }
        });
    </script>
</body>
</html>
