#!/usr/bin/env python3
"""
Diagnostic script for Instagram connection issue
"""

import sys
import os
sys.path.append('integrations')

def diagnose_instagram_connection():
    """Diagnose Instagram connection issue"""
    try:
        print("🔍 Diagnosing Instagram connection issue...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        from unipile_api import UnipileAPI
        
        print("\n1. Testing Instagram API initialization...")
        instagram = InstagramMessaging()
        print("✅ Instagram API initialized")
        print(f"   API Key: {instagram.unipile_client.api_key[:20]}...")
        print(f"   Base URL: {instagram.unipile_client.base_url}")
        
        print("\n2. Testing direct UnipileAPI call...")
        unipile = UnipileAPI(api_key=instagram.unipile_client.api_key)
        
        # Force refresh accounts cache
        print("   Forcing accounts cache refresh...")
        accounts = unipile.get_accounts(force_refresh=True)
        
        print(f"   Accounts response: {accounts}")
        
        if "error" in accounts:
            print(f"❌ Error getting accounts: {accounts['error']}")
            return False
        
        print(f"   Total accounts found: {len(accounts.get('items', []))}")
        
        print("\n3. Analyzing all accounts...")
        for i, account in enumerate(accounts.get('items', [])):
            print(f"   Account {i+1}:")
            print(f"     ID: {account.get('id', 'N/A')}")
            print(f"     Type: {account.get('type', 'N/A')}")
            print(f"     Username: {account.get('username', 'N/A')}")
            print(f"     Name: {account.get('name', 'N/A')}")
            print(f"     Provider: {account.get('provider', 'N/A')}")
            print(f"     Status: {account.get('status', 'N/A')}")
            print()
        
        print("4. Looking for Instagram accounts specifically...")
        instagram_accounts = []
        
        for account in accounts.get('items', []):
            account_type = account.get('type', '').upper()
            provider = account.get('provider', '').upper()
            
            print(f"   Checking account: type='{account_type}', provider='{provider}'")
            
            if account_type == "INSTAGRAM" or provider == "INSTAGRAM":
                instagram_accounts.append(account)
                print(f"   ✅ Found Instagram account: {account.get('id')}")
        
        if instagram_accounts:
            print(f"\n✅ Found {len(instagram_accounts)} Instagram account(s)!")
            for acc in instagram_accounts:
                print(f"   - ID: {acc.get('id')}")
                print(f"   - Username: {acc.get('username', 'N/A')}")
                print(f"   - Type: {acc.get('type', 'N/A')}")
        else:
            print("\n❌ No Instagram accounts found in the response")
            print("   This could mean:")
            print("   1. Account was connected to a different API key")
            print("   2. Account type/provider field is different than expected")
            print("   3. Account connection failed or was disconnected")
        
        print("\n5. Testing Instagram API account detection...")
        instagram_status = instagram.get_connection_status()
        print(f"   Instagram connection status: {instagram_status}")
        
        return len(instagram_accounts) > 0
        
    except Exception as e:
        print(f"❌ Error in diagnosis: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_api_key():
    """Test with the other API key to see if account is there"""
    try:
        print("\n6. Testing with alternative API key...")
        
        from unipile_api import UnipileAPI
        
        # Test with the WhatsApp API key
        alt_api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
        print(f"   Testing with API key: {alt_api_key[:20]}...")
        
        unipile_alt = UnipileAPI(api_key=alt_api_key)
        accounts_alt = unipile_alt.get_accounts(force_refresh=True)
        
        if "error" in accounts_alt:
            print(f"   ❌ Error with alt API key: {accounts_alt['error']}")
            return False
        
        print(f"   Total accounts with alt key: {len(accounts_alt.get('items', []))}")
        
        instagram_accounts_alt = []
        for account in accounts_alt.get('items', []):
            account_type = account.get('type', '').upper()
            if account_type == "INSTAGRAM":
                instagram_accounts_alt.append(account)
                print(f"   ✅ Found Instagram account with alt key: {account.get('id')}")
        
        if instagram_accounts_alt:
            print(f"   🎯 Instagram account found with alternative API key!")
            print(f"   This means the account was connected to the WhatsApp API key instead.")
            return True
        else:
            print(f"   No Instagram accounts found with alternative API key either.")
            return False
        
    except Exception as e:
        print(f"❌ Error testing alternative API key: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Instagram Connection Diagnostic Tool\n")
    
    # Run main diagnosis
    found_instagram = diagnose_instagram_connection()
    
    if not found_instagram:
        # Test with alternative API key
        found_with_alt = test_with_different_api_key()
        
        if found_with_alt:
            print("\n💡 SOLUTION FOUND:")
            print("   Your Instagram account is connected to the WhatsApp API key.")
            print("   You need to either:")
            print("   1. Update Instagram config to use the WhatsApp API key, OR")
            print("   2. Reconnect Instagram with the Instagram-specific API key")
    
    print("\n🏁 Diagnosis completed.")
