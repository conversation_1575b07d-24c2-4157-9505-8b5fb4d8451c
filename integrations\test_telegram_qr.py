#!/usr/bin/env python3
"""
Test script for Telegram QR code authentication
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from telegram_integration.telegram_api import TelegramMessaging

def test_qr_generation():
    """Test QR code generation"""
    print("🔧 Testing Telegram QR Code Generation...")
    
    try:
        # Initialize Telegram messaging
        telegram = TelegramMessaging()
        print("✅ TelegramMessaging initialized")
        
        # Check connection status
        print("\n📡 Checking connection status...")
        status = telegram.check_qr_connection_status()
        print(f"Connected: {status.get('connected')}")
        print(f"QR Required: {status.get('qr_required')}")
        
        if status.get('connected'):
            print("✅ Already connected!")
            return
        
        # Generate QR code
        print("\n📱 Generating QR code...")
        result = telegram.authenticate_account()
        
        if result.get('success'):
            print("✅ QR code generated successfully!")
            print(f"QR Required: {result.get('qr_required')}")
            print(f"Has QR Code: {'qr_code' in result}")
            if 'qr_code' in result:
                qr_length = len(result['qr_code'])
                print(f"QR Code Length: {qr_length} characters")
                print(f"QR Code Preview: {result['qr_code'][:50]}...")
        else:
            print(f"❌ QR generation failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_qr_generation()
