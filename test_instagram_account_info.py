#!/usr/bin/env python3
"""
Test script to debug Instagram account info issue
"""

import sys
import os
sys.path.append('integrations')

def test_instagram_account_info():
    """Test Instagram account info method directly"""
    try:
        print("Testing Instagram account info...")
        
        from instagram_integration.instagram_api import InstagramMessaging
        
        print("Initializing Instagram API...")
        instagram = InstagramMessaging()
        
        print("✅ Instagram API initialized successfully")
        
        # Test get_account_info method directly
        print("Testing get_account_info method...")
        result = instagram.get_account_info()
        
        print(f"Account info result: {result}")
        print(f"Result type: {type(result)}")
        
        # Check what fields are in the result
        if isinstance(result, dict):
            print(f"Result keys: {list(result.keys())}")
            
            if "error" in result:
                print(f"Error message: {result['error']}")
                
            if "success" in result:
                print(f"Success field: {result['success']}")
            else:
                print("No 'success' field in result")
                
        return result
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_api_endpoint():
    """Test the API endpoint directly"""
    try:
        print("\nTesting API endpoint...")
        
        import requests
        import json
        
        response = requests.get('http://localhost:8000/api/instagram/account-info', timeout=10)
        print(f'API Response - Status Code: {response.status_code}')
        
        if response.status_code == 200:
            print(f'Response: {json.dumps(response.json(), indent=2)}')
        else:
            print(f'Error Response: {response.text}')
            
    except Exception as e:
        print(f"❌ API Error: {e}")

if __name__ == "__main__":
    print("🔧 Testing Instagram account info issue...\n")
    
    # Test the method directly first
    result = test_instagram_account_info()
    
    if result:
        print("\n" + "="*50)
        # Test the API endpoint
        test_api_endpoint()
    
    print("\n🏁 Test completed.")
