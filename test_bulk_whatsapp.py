#!/usr/bin/env python3
"""
Test script for WhatsApp bulk messaging
"""

import sys
import os
sys.path.append('integrations')

from whatsapp_integration.whatsapp_api import WhatsAppMessaging

def test_bulk_messaging():
    """Test WhatsApp bulk messaging directly"""
    try:
        print("Initializing WhatsApp messaging...")
        whatsapp = WhatsAppMessaging()
        
        print("Testing connection...")
        connection_status = whatsapp.test_connection()
        print(f"Connection status: {connection_status}")
        
        if not connection_status.get("connected"):
            print("WhatsApp not connected. Please authenticate first.")
            return
        
        print("Testing bulk messaging...")
        recipients = [{"phone_number": "+2348033983592"}]
        message = "Test bulk message from direct API call"
        
        print(f"Sending to: {recipients}")
        print(f"Message: {message}")
        
        results = whatsapp.send_bulk_messages(recipients, message, delay=0.5)
        
        print(f"Results: {results}")
        
        # Check results
        for result in results:
            if result.get("result", {}).get("success"):
                print(f"✅ Message sent successfully to {result['phone_number']}")
            else:
                print(f"❌ Failed to send to {result['phone_number']}: {result.get('result', {}).get('error')}")
                
    except Exception as e:
        print(f"Error in test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_bulk_messaging()
