<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Authentication - Unified Messaging</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .auth-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #25D366;
        }

        .auth-section h2 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .status-connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-loading {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .qr-code-container {
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 10px;
            border: 2px dashed #e1e5e9;
            margin: 20px 0;
        }

        .qr-code {
            max-width: 256px;
            margin: 0 auto;
            border-radius: 10px;
        }

        .qr-placeholder {
            width: 256px;
            height: 256px;
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            border-radius: 10px;
            color: #6c757d;
            font-size: 18px;
            flex-direction: column;
        }

        .qr-instructions {
            margin-top: 20px;
            padding: 15px;
            background: #e7f3ff;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-danger {
            background: #dc3545;
        }

        .result-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: 1px solid #e1e5e9;
        }

        .card h3 {
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25D366;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .connection-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-dot.connected {
            background: #25D366;
            box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.3);
        }

        .status-dot.disconnected {
            background: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
        }

        .status-dot.loading {
            background: #ffc107;
            box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.3);
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .whatsapp-icon {
            font-size: 1.5em;
            margin-right: 10px;
        }

        /* Chat History Styles */
        .chat-history-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 5px solid #25D366;
        }

        .conversations-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
        }

        .conversation-item {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .conversation-item:hover {
            background-color: #f8f9fa;
        }

        .conversation-item:last-child {
            border-bottom: none;
        }

        .conversation-item.active {
            background-color: #e7f3ff;
            border-left: 4px solid #25D366;
        }

        .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .conversation-name {
            font-weight: 600;
            color: #333;
        }

        .conversation-time {
            font-size: 0.85em;
            color: #6c757d;
        }

        .conversation-preview {
            color: #6c757d;
            font-size: 0.9em;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .unread-badge {
            background: #25D366;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.75em;
            font-weight: bold;
            min-width: 18px;
            text-align: center;
        }

        .chat-messages {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: white;
            padding: 15px;
        }

        .message-item {
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        .message-item.outgoing {
            align-items: flex-end;
        }

        .message-item.incoming {
            align-items: flex-start;
        }

        .message-bubble {
            max-width: 70%;
            padding: 10px 15px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message-bubble.outgoing {
            background: #25D366;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message-bubble.incoming {
            background: #f1f3f4;
            color: #333;
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 0.75em;
            color: #6c757d;
            margin-top: 5px;
            padding: 0 5px;
        }

        .message-sender {
            font-size: 0.8em;
            color: #25D366;
            font-weight: 600;
            margin-bottom: 3px;
            padding: 0 5px;
        }

        .chat-input-section {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .chat-input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #e1e5e9;
            border-radius: 20px;
            outline: none;
            resize: none;
        }

        .chat-input:focus {
            border-color: #25D366;
        }

        .send-btn {
            background: #25D366;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
        }

        .send-btn:hover {
            background: #128C7E;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: #25D366;
            color: white;
            border-radius: 8px 8px 0 0;
            margin-bottom: 0;
        }

        .chat-title {
            font-weight: 600;
            font-size: 1.1em;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .chat-controls button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .chat-controls button:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }

            .header {
                padding: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .auth-section {
                padding: 20px;
            }

            .grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><span class="whatsapp-icon">📱</span>WhatsApp Authentication</h1>
            <p>Connect your WhatsApp account to the Unified Messaging System</p>
        </div>

        <div class="content">
            <!-- Unipile QR Code Authentication -->
            <div class="auth-section">
                <h2>🚀 WhatsApp Authentication</h2>
                <div class="info">
                    <strong>Connect your WhatsApp account using QR code authentication via Unipile API.</strong>
                </div>

                <button class="btn" onclick="generateQRCode()">Generate QR Code</button>
                <button class="btn btn-secondary" onclick="refreshQRCode()">Refresh QR Code</button>

                <div id="unipileResult"></div>

                <!-- QR Code Display Area -->
                <div class="qr-code-container" id="qrCodeContainer" style="display: none;">
                    <div id="qrCodeArea" class="qr-placeholder">
                        <div>📱</div>
                        <div>QR Code will appear here</div>
                    </div>
                    <div class="qr-instructions">
                        <strong>📋 Instructions:</strong>
                        <ol style="text-align: left; margin-top: 10px;">
                            <li>Open WhatsApp on your phone</li>
                            <li>Go to Settings → Linked Devices</li>
                            <li>Tap "Link a Device"</li>
                            <li>Scan this QR code</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Connection Status -->
            <div class="auth-section">
                <h2>🔗 Connection Status</h2>
                <div id="connectionStatus" class="status-indicator status-loading">
                    <span class="loading-spinner"></span> Checking connection status...
                </div>
                <button class="btn" onclick="checkConnectionStatus()">Refresh Status</button>
            </div>

            <!-- Chat History Section -->
            <div class="chat-history-section" id="chatHistorySection" style="display: none;">
                <h2>💬 Chat History</h2>
                <div class="info">
                    <strong>View your WhatsApp conversations and message history.</strong>
                </div>

                <div style="display: flex; gap: 20px; margin-top: 20px;">
                    <!-- Conversations List -->
                    <div style="flex: 1;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                            <h3>📋 Conversations</h3>
                            <button class="btn" onclick="loadConversations()">Refresh</button>
                        </div>
                        <div id="conversationsList" class="conversations-list">
                            <div style="padding: 20px; text-align: center; color: #6c757d;">
                                Click "Refresh" to load conversations
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div style="flex: 2;">
                        <div id="chatHeader" class="chat-header" style="display: none;">
                            <div class="chat-title" id="chatTitle">Select a conversation</div>
                            <div class="chat-controls">
                                <button onclick="refreshMessages()">🔄 Refresh</button>
                                <button onclick="clearChat()">❌ Clear</button>
                            </div>
                        </div>
                        <div id="chatMessages" class="chat-messages">
                            <div style="padding: 40px; text-align: center; color: #6c757d;">
                                <div style="font-size: 3em; margin-bottom: 10px;">💬</div>
                                <div>Select a conversation to view messages</div>
                            </div>
                        </div>

                        <!-- Chat Input (for future use) -->
                        <div id="chatInputSection" class="chat-input-section" style="display: none;">
                            <textarea id="chatInput" class="chat-input" placeholder="Type a message..." rows="1"></textarea>
                            <button class="send-btn" onclick="sendChatMessage()">📤</button>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter Options -->
                <div style="margin-top: 20px; padding: 15px; background: white; border-radius: 8px; border: 1px solid #e1e5e9;">
                    <h4>🔍 Search & Filter</h4>
                    <div style="display: flex; gap: 15px; margin-top: 10px;">
                        <div class="form-group" style="flex: 1; margin-bottom: 0;">
                            <input type="text" id="searchPhone" placeholder="Search by phone number (+**********)" style="margin-bottom: 0;">
                        </div>
                        <button class="btn" onclick="searchChatHistory()">Search History</button>
                        <button class="btn btn-secondary" onclick="exportChatHistory()">📥 Export</button>
                    </div>
                </div>

                <div id="chatHistoryResult"></div>
            </div>

            <!-- Testing Section -->
            <div class="grid">
                <div class="card">
                    <h3>🧪 Test Messaging</h3>
                    <div class="form-group">
                        <label for="testPhoneNumber">Test Phone Number:</label>
                        <input type="tel" id="testPhoneNumber" placeholder="+**********">
                    </div>
                    <div class="form-group">
                        <label for="testMessage">Test Message:</label>
                        <textarea id="testMessage" rows="3" placeholder="Hello! This is a test message from WhatsApp integration."></textarea>
                    </div>
                    <button class="btn" onclick="sendTestMessage()">Send Test Message</button>
                    <div id="testResult"></div>
                </div>

                <div class="card">
                    <h3>📊 Account Information</h3>
                    <button class="btn" onclick="getAccountInfo()">Get Account Info</button>
                    <div id="accountInfoResult"></div>
                </div>

                <div class="card">
                    <h3>📈 Bulk Messaging</h3>
                    <div class="form-group">
                        <label for="bulkRecipients">Recipients (JSON format):</label>
                        <textarea id="bulkRecipients" rows="4" placeholder='[{"phone_number": "+**********", "name": "John"}, {"phone_number": "+**********", "name": "Jane"}]'></textarea>
                    </div>
                    <div class="form-group">
                        <label for="bulkMessage">Message Template:</label>
                        <textarea id="bulkMessage" rows="2" placeholder="Hello {name}! This is a bulk message."></textarea>
                    </div>
                    <button class="btn" onclick="sendBulkMessages()">Send Bulk Messages</button>
                    <div id="bulkResult"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Auto-refresh connection status every 30 seconds
        setInterval(checkConnectionStatus, 30000);

        // Global variables for chat history
        let currentChatId = null;
        let currentChatPhone = null;
        let conversations = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkConnectionStatus();
        });

        function checkConnectionStatus() {
            showLoading('connectionStatus', 'Checking connection status...');

            fetch('/api/whatsapp/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const status = data.status;
                    let html = '<div class="connection-status">';

                    // Unipile status
                    if (status.available) {
                        if (status.connected) {
                            html += '<span class="status-dot connected"></span> Unipile WhatsApp: Connected (' + status.accounts.length + ' accounts)<br>';
                            status.accounts.forEach(account => {
                                html += '&nbsp;&nbsp;• ' + (account.username || account.name || account.account_id) + '<br>';
                            });
                        } else {
                            html += '<span class="status-dot disconnected"></span> Unipile WhatsApp: Available but not connected<br>';
                        }
                    } else {
                        html += '<span class="status-dot disconnected"></span> Unipile WhatsApp: Not available<br>';
                    }

                    html += '</div>';

                    updateConnectionStatus(status.connected ? 'connected' : 'disconnected', html);

                    // Show/hide chat history section based on connection status
                    const chatHistorySection = document.getElementById('chatHistorySection');
                    if (status.connected) {
                        chatHistorySection.style.display = 'block';
                    } else {
                        chatHistorySection.style.display = 'none';
                    }
                } else {
                    updateConnectionStatus('disconnected', '❌ Failed to get status: ' + data.error);
                    document.getElementById('chatHistorySection').style.display = 'none';
                }
            })
            .catch(error => {
                updateConnectionStatus('disconnected', '❌ Status check error: ' + error.message);
                document.getElementById('chatHistorySection').style.display = 'none';
            });
        }



        function generateQRCode() {
            showLoading('unipileResult', 'Generating QR code...');

            fetch('/api/whatsapp/authenticate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('QR Code Response:', data); // Debug log

                if (data.success && data.qr_code) {
                    // Clear the placeholder and show the QR code image
                    const qrCodeArea = document.getElementById('qrCodeArea');
                    qrCodeArea.innerHTML = '<img src="' + data.qr_code + '" alt="WhatsApp QR Code" class="qr-code" style="max-width: 256px; height: auto; border-radius: 10px;">';
                    qrCodeArea.className = ''; // Remove placeholder class

                    document.getElementById('qrCodeContainer').style.display = 'block';
                    showResult('unipileResult', '✅ QR code generated! Please scan with your WhatsApp mobile app.', 'success');

                    // Auto-refresh QR code every 60 seconds
                    setTimeout(refreshQRCode, 60000);

                    // Also refresh connection status to check if connected
                    setTimeout(checkConnectionStatus, 5000);
                } else if (data.success && !data.qr_required) {
                    showResult('unipileResult', '✅ WhatsApp account already connected!', 'success');
                    document.getElementById('qrCodeContainer').style.display = 'none';
                    checkConnectionStatus(); // Refresh status
                } else {
                    showResult('unipileResult', '❌ Failed to generate QR code: ' + (data.error || 'Unknown error'), 'error');
                    console.error('QR Code Error:', data);
                }
            })
            .catch(error => {
                showResult('unipileResult', '❌ QR code generation error: ' + error.message, 'error');
                console.error('QR Code Fetch Error:', error);
            });
        }

        function refreshQRCode() {
            // Reset QR code area to placeholder
            const qrCodeArea = document.getElementById('qrCodeArea');
            qrCodeArea.innerHTML = '<div>📱</div><div>Generating new QR code...</div>';
            qrCodeArea.className = 'qr-placeholder';

            // Generate new QR code
            generateQRCode();
        }



        function sendTestMessage() {
            const phoneNumber = document.getElementById('testPhoneNumber').value;
            const message = document.getElementById('testMessage').value;
            
            if (!phoneNumber || !message) {
                showResult('testResult', '❌ Please enter both phone number and message', 'error');
                return;
            }
            
            showLoading('testResult', 'Sending test message...');
            
            fetch('/api/messaging/send', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    platform: 'whatsapp',
                    recipient: phoneNumber,
                    message: message
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const method = data.method || 'unknown';
                    showResult('testResult', `✅ Test message sent successfully via ${method}!`, 'success');
                } else {
                    showResult('testResult', '❌ Failed to send test message: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('testResult', '❌ Error: ' + error.message, 'error');
            });
        }

        function getAccountInfo() {
            showLoading('accountInfoResult', 'Getting account information...');

            fetch('/api/whatsapp/status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let html = '<div class="info"><strong>📊 Account Information:</strong><br>';

                    if (data.status.connected) {
                        html += '<strong>Unipile WhatsApp Accounts:</strong><br>';
                        data.status.accounts.forEach(account => {
                            html += '• ' + (account.username || account.name || account.account_id) + '<br>';
                            if (account.phone_number) {
                                html += '&nbsp;&nbsp;Phone: ' + account.phone_number + '<br>';
                            }
                        });
                    } else {
                        html += 'No WhatsApp accounts connected via Unipile<br>';
                    }

                    html += '<strong>Last Check:</strong> ' + data.status.last_check + '<br>';
                    html += '</div>';
                    document.getElementById('accountInfoResult').innerHTML = html;
                } else {
                    showResult('accountInfoResult', '❌ Failed to get account info: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('accountInfoResult', '❌ Error: ' + error.message, 'error');
            });
        }

        function sendBulkMessages() {
            const recipientsText = document.getElementById('bulkRecipients').value;
            const messageTemplate = document.getElementById('bulkMessage').value;
            
            if (!recipientsText || !messageTemplate) {
                showResult('bulkResult', '❌ Please fill in both recipients and message template', 'error');
                return;
            }
            
            let recipients;
            try {
                recipients = JSON.parse(recipientsText);
            } catch (e) {
                showResult('bulkResult', '❌ Invalid JSON format for recipients', 'error');
                return;
            }
            
            showLoading('bulkResult', `Sending bulk messages to ${recipients.length} recipients...`);
            
            // Convert to campaign format
            const campaign = {
                whatsapp: recipients.map(r => ({
                    contact: r.phone_number,
                    message: messageTemplate.replace('{name}', r.name || 'there')
                }))
            };
            
            fetch('/api/messaging/bulk', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    campaign: campaign,
                    delay: 2.0
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const summary = data.campaign_summary;
                    let html = `<div class="success">✅ Bulk messaging completed!<br>`;
                    html += `Successful: ${summary.successful}, Failed: ${summary.failed}<br>`;
                    html += `Success Rate: ${summary.success_rate.toFixed(1)}%</div>`;
                    document.getElementById('bulkResult').innerHTML = html;
                } else {
                    showResult('bulkResult', '❌ Bulk messaging failed: ' + data.error, 'error');
                }
            })
            .catch(error => {
                showResult('bulkResult', '❌ Error: ' + error.message, 'error');
            });
        }

        // Utility functions
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }

        function showLoading(elementId, message = 'Loading...') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="info"><span class="loading-spinner"></span> ${message}</div>`;
        }

        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            statusElement.className = `status-indicator status-${status}`;
            statusElement.innerHTML = message;
        }

        // Chat History Functions
        function loadConversations() {
            showLoading('conversationsList', 'Loading conversations...');

            fetch('/api/whatsapp/conversations?limit=50')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.success) {
                    conversations = data.data.conversations || [];
                    displayConversations(conversations);
                } else {
                    const error = data.error || data.data?.error || 'Failed to load conversations';
                    document.getElementById('conversationsList').innerHTML =
                        `<div style="padding: 20px; text-align: center; color: #dc3545;">❌ ${error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('conversationsList').innerHTML =
                    `<div style="padding: 20px; text-align: center; color: #dc3545;">❌ Error: ${error.message}</div>`;
            });
        }

        function displayConversations(convs) {
            const container = document.getElementById('conversationsList');

            if (!convs || convs.length === 0) {
                container.innerHTML = '<div style="padding: 20px; text-align: center; color: #6c757d;">No conversations found</div>';
                return;
            }

            let html = '';
            convs.forEach(conv => {
                const participantName = conv.participants && conv.participants.length > 0
                    ? conv.participants[0]
                    : 'Unknown Contact';
                const phoneNumber = conv.phone_numbers && conv.phone_numbers.length > 0
                    ? conv.phone_numbers[0]
                    : 'Unknown';
                const lastMessage = conv.last_message || 'No messages';
                const lastTime = conv.last_message_time ? new Date(conv.last_message_time).toLocaleString() : '';
                const unreadCount = conv.unread_count || 0;

                html += `
                    <div class="conversation-item" onclick="selectConversation('${conv.chat_id}', '${phoneNumber}', '${participantName}')">
                        <div class="conversation-header">
                            <div class="conversation-name">${participantName}</div>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                ${unreadCount > 0 ? `<span class="unread-badge">${unreadCount}</span>` : ''}
                                <div class="conversation-time">${lastTime}</div>
                            </div>
                        </div>
                        <div class="conversation-preview">${phoneNumber}</div>
                        <div class="conversation-preview">${lastMessage}</div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function selectConversation(chatId, phoneNumber, participantName) {
            // Update active conversation
            currentChatId = chatId;
            currentChatPhone = phoneNumber;

            // Update UI
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.conversation-item').classList.add('active');

            // Update chat header
            document.getElementById('chatTitle').textContent = `${participantName} (${phoneNumber})`;
            document.getElementById('chatHeader').style.display = 'flex';

            // Load messages for this conversation
            loadChatMessages(chatId);
        }

        function loadChatMessages(chatId) {
            showLoading('chatMessages', 'Loading messages...');

            fetch(`/api/whatsapp/chat/${chatId}/messages?limit=50`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.success) {
                    displayChatMessages(data.data.messages || []);
                } else {
                    const error = data.error || data.data?.error || 'Failed to load messages';
                    document.getElementById('chatMessages').innerHTML =
                        `<div style="padding: 20px; text-align: center; color: #dc3545;">❌ ${error}</div>`;
                }
            })
            .catch(error => {
                document.getElementById('chatMessages').innerHTML =
                    `<div style="padding: 20px; text-align: center; color: #dc3545;">❌ Error: ${error.message}</div>`;
            });
        }

        function displayChatMessages(messages) {
            const container = document.getElementById('chatMessages');

            if (!messages || messages.length === 0) {
                container.innerHTML = '<div style="padding: 20px; text-align: center; color: #6c757d;">No messages in this conversation</div>';
                return;
            }

            let html = '';
            messages.forEach(msg => {
                const isOutgoing = msg.is_outgoing;
                const messageClass = isOutgoing ? 'outgoing' : 'incoming';
                const bubbleClass = isOutgoing ? 'outgoing' : 'incoming';
                const senderName = msg.sender_name || (isOutgoing ? 'You' : 'Contact');
                const timestamp = msg.timestamp ? new Date(msg.timestamp).toLocaleString() : '';
                const messageText = msg.text || '[Media message]';

                html += `
                    <div class="message-item ${messageClass}">
                        ${!isOutgoing ? `<div class="message-sender">${senderName}</div>` : ''}
                        <div class="message-bubble ${bubbleClass}">
                            ${messageText}
                        </div>
                        <div class="message-time">${timestamp}</div>
                    </div>
                `;
            });

            container.innerHTML = html;

            // Scroll to bottom
            container.scrollTop = container.scrollHeight;
        }

        function refreshMessages() {
            if (currentChatId) {
                loadChatMessages(currentChatId);
            }
        }

        function clearChat() {
            currentChatId = null;
            currentChatPhone = null;
            document.getElementById('chatHeader').style.display = 'none';
            document.getElementById('chatMessages').innerHTML = `
                <div style="padding: 40px; text-align: center; color: #6c757d;">
                    <div style="font-size: 3em; margin-bottom: 10px;">💬</div>
                    <div>Select a conversation to view messages</div>
                </div>
            `;

            // Remove active class from conversations
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        function searchChatHistory() {
            const phoneNumber = document.getElementById('searchPhone').value.trim();

            if (!phoneNumber) {
                showResult('chatHistoryResult', '❌ Please enter a phone number to search', 'error');
                return;
            }

            showLoading('chatHistoryResult', 'Searching chat history...');

            fetch(`/api/whatsapp/history?phone_number=${encodeURIComponent(phoneNumber)}&limit=50`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.success) {
                    const messages = data.data.messages || [];
                    if (messages.length > 0) {
                        showResult('chatHistoryResult', `✅ Found ${messages.length} messages for ${phoneNumber}`, 'success');
                        // Display the messages in the chat area
                        displayChatMessages(messages);
                        document.getElementById('chatTitle').textContent = `Search Results: ${phoneNumber}`;
                        document.getElementById('chatHeader').style.display = 'flex';
                    } else {
                        showResult('chatHistoryResult', `ℹ️ No messages found for ${phoneNumber}`, 'info');
                    }
                } else {
                    const error = data.error || data.data?.error || 'Search failed';
                    showResult('chatHistoryResult', `❌ ${error}`, 'error');
                }
            })
            .catch(error => {
                showResult('chatHistoryResult', `❌ Search error: ${error.message}`, 'error');
            });
        }

        function exportChatHistory() {
            if (!conversations || conversations.length === 0) {
                showResult('chatHistoryResult', '❌ No conversations to export. Load conversations first.', 'error');
                return;
            }

            // Create CSV content
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Participant,Phone Number,Last Message,Last Message Time,Unread Count\n";

            conversations.forEach(conv => {
                const participant = (conv.participants && conv.participants[0]) || 'Unknown';
                const phone = (conv.phone_numbers && conv.phone_numbers[0]) || 'Unknown';
                const lastMsg = (conv.last_message || '').replace(/"/g, '""'); // Escape quotes
                const lastTime = conv.last_message_time || '';
                const unread = conv.unread_count || 0;

                csvContent += `"${participant}","${phone}","${lastMsg}","${lastTime}",${unread}\n`;
            });

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `whatsapp_conversations_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showResult('chatHistoryResult', '✅ Chat history exported successfully!', 'success');
        }
    </script>
</body>
</html>
