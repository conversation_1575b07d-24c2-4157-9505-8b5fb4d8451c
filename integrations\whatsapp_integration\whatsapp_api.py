"""
WhatsApp Messaging API Integration
Handles authentication and messaging for WhatsApp accounts
Uses Unipile API for unified WhatsApp messaging with QR code authentication
"""

import json
import time
import qrcode
import io
import base64
from typing import Dict, List
import logging
from datetime import datetime
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class WhatsAppMessaging:
    def __init__(self, config_path: str = "integrations/whatsapp_integration/config.json"):
        """Initialize WhatsApp messaging client with Unipile API"""
        self.config_path = config_path

        # Setup logging first
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.config = self._load_config()

        # Unipile API setup (only method)
        self.unipile_client = None
        try:
            # Get API key from config or use WhatsApp-specific default
            unipile_config = self.config.get("unipile", {})
            api_key = unipile_config.get("api_key", "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk=")
            base_url = unipile_config.get("api_url", "https://api8.unipile.com:13814")

            if not api_key:
                api_key = "K0Py2YdG.SazddZm5laRo0Bk9kZ0cKnNt8PLt/AJj15NEGsM7lrk="
                self.logger.warning("No API key found in config, using WhatsApp default")

            # Initialize directly with correct API key
            from unipile_api import UnipileAPI
            self.unipile_client = UnipileAPI(api_key=api_key, base_url=base_url)

            self.logger.info(f"Unipile API client initialized for WhatsApp with key: {api_key[:20]}...")
        except Exception as e:
            self.logger.warning(f"Failed to initialize Unipile API: {e}")
            # Don't raise exception, allow graceful degradation
            self.unipile_client = None

        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 1.0  # 1 second between requests

        # Authentication status
        self.connection_status = {
            "connected": False,
            "qr_code": None,
            "last_check": None,
            "accounts": []
        }
    
    def _load_config(self) -> Dict:
        """Load configuration from JSON file"""
        try:
            with open(self.config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            self.logger.warning(f"Config file not found: {self.config_path}")
            return {}
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in config file: {e}")
            return {}
    
    def _save_config(self):
        """Save current configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
            with open(self.config_path, 'w') as f:
                json.dump(self.config, f, indent=2)
            self.logger.info("WhatsApp configuration saved")
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
    
    def _rate_limit(self):
        """Implement rate limiting"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        self.last_request_time = time.time()
    
    def authenticate_account(self) -> Dict:
        """
        Authenticate WhatsApp account via Unipile
        Generates QR code for WhatsApp Web authentication
        """
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Check if account is already connected
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if whatsapp_accounts:
                self.connection_status["connected"] = True
                self.connection_status["accounts"] = whatsapp_accounts
                self.connection_status["last_check"] = datetime.now().isoformat()
                self.logger.info("WhatsApp account already connected via Unipile")
                return {
                    "success": True,
                    "message": "WhatsApp account connected",
                    "accounts": whatsapp_accounts,
                    "qr_required": False
                }

            # Generate QR code for authentication
            qr_response = self.unipile_client.generate_whatsapp_qr()

            # Extract QR code from the new response format
            checkpoint = qr_response.get("checkpoint", {})
            qr_code_data = checkpoint.get("qrcode")
            account_id = qr_response.get("account_id")

            if qr_code_data:
                # Generate QR code image
                qr_img = qrcode.make(qr_code_data)

                # Convert to base64 for HTML display
                img_buffer = io.BytesIO()
                qr_img.save(img_buffer, format='PNG')
                img_buffer.seek(0)
                qr_base64 = base64.b64encode(img_buffer.getvalue()).decode()

                self.connection_status["qr_code"] = f"data:image/png;base64,{qr_base64}"
                self.connection_status["last_check"] = datetime.now().isoformat()

                # Store account_id if provided
                if account_id:
                    self.connection_status["account_id"] = account_id

                return {
                    "success": True,
                    "message": "QR code generated. Please scan with WhatsApp mobile app.",
                    "qr_code": self.connection_status["qr_code"],
                    "qr_data": qr_code_data,
                    "account_id": account_id,
                    "qr_required": True
                }
            else:
                return {"error": "Failed to generate QR code"}

        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            return {"error": str(e)}
    
    def check_connection_status(self) -> Dict:
        """Check current connection status for Unipile WhatsApp integration"""
        status = {
            "available": bool(self.unipile_client),
            "connected": False,
            "accounts": [],
            "qr_code": self.connection_status.get("qr_code"),
            "last_check": datetime.now().isoformat()
        }

        # Check Unipile connection
        if self.unipile_client:
            try:
                accounts = self.unipile_client.get_accounts()
                whatsapp_accounts = [acc for acc in accounts.get("items", [])
                                   if acc.get("type", "").upper() == "WHATSAPP"]
                status["connected"] = len(whatsapp_accounts) > 0
                status["accounts"] = whatsapp_accounts

                # Update internal status
                self.connection_status["connected"] = status["connected"]
                self.connection_status["accounts"] = whatsapp_accounts

            except Exception as e:
                self.logger.error(f"Error checking Unipile status: {e}")
                status["error"] = str(e)

        self.connection_status["last_check"] = status["last_check"]
        return status
    
    def send_message(self, phone_number: str, message: str, **kwargs) -> Dict:
        """Send WhatsApp message via Unipile API"""
        if not self.unipile_client:
            return {
                "success": False,
                "error": "Unipile client not available. Please check API configuration."
            }

        # Check if WhatsApp account is connected
        status = self.check_connection_status()
        if not status.get("connected"):
            return {
                "success": False,
                "error": "WhatsApp account not connected. Please authenticate first using QR code."
            }

        # Clean and validate phone number
        original_phone = phone_number
        phone_number = phone_number.replace("+", "").replace(" ", "").replace("-", "").replace("(", "").replace(")", "")

        if not phone_number.isdigit():
            return {
                "success": False,
                "error": f"Invalid phone number format: {original_phone}. Please use format: +**********"
            }

        # Add + back for international format
        if not phone_number.startswith("+"):
            phone_number = "+" + phone_number

        self._rate_limit()

        try:
            self.logger.info(f"Sending WhatsApp message to {phone_number}: {message[:50]}...")

            # Get connected WhatsApp accounts
            accounts = self.unipile_client.get_accounts()
            if "error" in accounts:
                return {
                    "success": False,
                    "error": f"Failed to get accounts: {accounts['error']}",
                    "recipient": phone_number
                }

            # Find WhatsApp account
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if not whatsapp_accounts:
                return {
                    "success": False,
                    "error": "No WhatsApp account connected. Please authenticate first.",
                    "recipient": phone_number
                }

            account_id = whatsapp_accounts[0]["id"]

            # Send message using the correct API flow
            result = self.unipile_client.send_message(account_id, phone_number, message, **kwargs)

            if "error" not in result:
                self.logger.info(f"WhatsApp message sent successfully to {phone_number}")
                return {
                    "success": True,
                    "result": result,
                    "method": "unipile",
                    "message_id": result.get("message_id") or result.get("id"),
                    "recipient": phone_number,
                    "account_id": account_id,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                error_msg = result.get('error', 'Unknown error')
                self.logger.error(f"Unipile API error: {error_msg}")
                return {
                    "success": False,
                    "error": f"Failed to send message: {error_msg}",
                    "recipient": phone_number
                }
        except Exception as e:
            self.logger.error(f"Exception sending WhatsApp message: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "recipient": phone_number
            }
    
    def send_bulk_messages(self, recipients: List[Dict], message_template: str, delay: float = 2.0) -> List[Dict]:
        """Send WhatsApp messages to multiple recipients via Unipile"""
        if not self.unipile_client:
            return [{"error": "Unipile client not available"}]

        results = []

        for recipient in recipients:
            phone_number = recipient.get("phone_number") or recipient.get("contact")
            if not phone_number:
                continue

            # Personalize message - handle both template and simple message cases
            try:
                # Try to format as template first
                personalized_message = message_template.format(**recipient)
            except (KeyError, ValueError):
                # If formatting fails, use message as-is
                personalized_message = message_template

            result = self.send_message(phone_number, personalized_message)
            results.append({
                "phone_number": phone_number,
                "result": result,
                "method": result.get("method", "unipile"),
                "timestamp": datetime.now().isoformat()
            })

            if delay > 0:
                time.sleep(delay)

        return results
    
    def update_config(self, **kwargs) -> Dict:
        """Update WhatsApp configuration"""
        for key, value in kwargs.items():
            if key in ["unipile_api_key", "webhook_settings", "message_templates"]:
                self.config[key] = value

        self._save_config()
        return {"success": True, "message": "Configuration updated"}
    
    def test_connection(self) -> Dict:
        """Test Unipile WhatsApp connection"""
        if not self.unipile_client:
            return {"available": False, "connected": False, "error": "Unipile client not available"}

        try:
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            return {
                "available": True,
                "connected": len(whatsapp_accounts) > 0,
                "accounts": whatsapp_accounts
            }
        except Exception as e:
            return {
                "available": True,
                "connected": False,
                "error": str(e)
            }

    def is_configured(self) -> bool:
        """Check if WhatsApp is properly configured"""
        return self.unipile_client is not None

    def _extract_phone_from_identifier(self, identifier: str) -> str:
        """Extract and format phone number from various identifier formats"""
        if not identifier:
            return "Unknown"

        # Remove common prefixes and suffixes
        clean_id = identifier.replace("@c.us", "").replace("@s.whatsapp.net", "")

        # Check if it looks like a phone number
        if clean_id.replace("+", "").replace("-", "").replace(" ", "").isdigit():
            # Format as international number
            if not clean_id.startswith("+"):
                clean_id = "+" + clean_id
            return clean_id

        return identifier

    def get_conversation_details(self, chat_id: str) -> Dict:
        """Get detailed information about a specific conversation including participant phone numbers"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        try:
            # Get connected WhatsApp accounts
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if not whatsapp_accounts:
                return {"error": "No WhatsApp account connected"}

            account_id = whatsapp_accounts[0]["id"]

            # Get recent messages to extract participant information
            messages_result = self.unipile_client.get_messages(account_id, chat_id, 20)

            if "error" in messages_result:
                return {"error": f"Failed to get messages: {messages_result['error']}"}

            participants = {}
            messages = messages_result.get("items", [])

            for message in messages:
                sender_id = message.get("sender_id")
                if sender_id and sender_id != account_id:
                    sender_name = message.get("sender_name") or "Unknown Contact"
                    phone = self._extract_phone_from_identifier(sender_id)

                    if sender_id not in participants:
                        participants[sender_id] = {
                            "name": sender_name,
                            "phone": phone,
                            "last_message_time": message.get("created_at"),
                            "message_count": 1
                        }
                    else:
                        participants[sender_id]["message_count"] += 1
                        # Update with more recent message time if available
                        if message.get("created_at"):
                            participants[sender_id]["last_message_time"] = message.get("created_at")

            return {
                "success": True,
                "chat_id": chat_id,
                "participants": list(participants.values()),
                "total_messages": len(messages)
            }

        except Exception as e:
            self.logger.error(f"Error getting conversation details: {e}")
            return {"error": str(e)}

    def get_conversations(self, limit: int = 50) -> Dict:
        """Get WhatsApp conversations/chats via Unipile API"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        # Check if WhatsApp account is connected
        status = self.check_connection_status()
        if not status.get("connected"):
            return {"error": "WhatsApp account not connected. Please authenticate first."}

        try:
            # Get connected WhatsApp accounts
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if not whatsapp_accounts:
                return {"error": "No WhatsApp account connected"}

            account_id = whatsapp_accounts[0]["id"]

            # Get chats for the WhatsApp account
            chats_result = self.unipile_client.get_chats(account_id, limit)

            if "error" in chats_result:
                return {"error": f"Failed to get conversations: {chats_result['error']}"}

            # Format conversations for better display
            conversations = []
            for chat in chats_result.get("items", []):
                chat_id = chat.get("id")

                # Extract participant information from attendees
                attendees = chat.get("attendees", [])
                participant_names = []
                participant_phones = []

                for attendee in attendees:
                    if attendee.get("identifier") != account_id:  # Exclude self
                        name = attendee.get("name") or attendee.get("display_name") or "Unknown"
                        phone = attendee.get("phone_number") or self._extract_phone_from_identifier(attendee.get("identifier", ""))
                        participant_names.append(name)
                        participant_phones.append(phone)

                # If no participant info from attendees, try to get from messages
                if not participant_phones and chat_id:
                    try:
                        # Get a few messages to extract participant info
                        messages_result = self.unipile_client.get_messages(account_id, chat_id, 5)
                        if "error" not in messages_result:
                            for message in messages_result.get("items", []):
                                sender_id = message.get("sender_id")
                                if sender_id and sender_id != account_id:
                                    # Try to extract phone number from sender_id or other fields
                                    sender_name = message.get("sender_name") or "Unknown Contact"
                                    phone = self._extract_phone_from_identifier(sender_id)

                                    if phone not in participant_phones:
                                        participant_phones.append(phone)
                                        participant_names.append(sender_name)
                                    break  # Just get the first non-self participant
                    except Exception as e:
                        self.logger.warning(f"Could not get participant info from messages for chat {chat_id}: {e}")

                # If still no participant info, use chat ID as fallback
                if not participant_phones:
                    participant_phones = [f"Chat-{chat_id[:8]}"]
                    participant_names = ["Unknown Contact"]

                conversation = {
                    "chat_id": chat_id,
                    "participants": participant_names,
                    "phone_numbers": participant_phones,
                    "last_message": chat.get("last_message", {}).get("text", ""),
                    "last_message_time": chat.get("last_message", {}).get("created_at"),
                    "unread_count": chat.get("unread_count", 0),
                    "created_at": chat.get("created_at"),
                    "updated_at": chat.get("updated_at")
                }
                conversations.append(conversation)

            return {
                "success": True,
                "conversations": conversations,
                "total": len(conversations),
                "account_id": account_id
            }

        except Exception as e:
            self.logger.error(f"Error getting conversations: {e}")
            return {"error": str(e)}

    def get_chat_messages(self, chat_id: str, limit: int = 50) -> Dict:
        """Get messages from a specific WhatsApp chat via Unipile API"""
        if not self.unipile_client:
            return {"error": "Unipile client not available"}

        # Check if WhatsApp account is connected
        status = self.check_connection_status()
        if not status.get("connected"):
            return {"error": "WhatsApp account not connected. Please authenticate first."}

        try:
            # Get connected WhatsApp accounts
            accounts = self.unipile_client.get_accounts()
            whatsapp_accounts = [acc for acc in accounts.get("items", [])
                               if acc.get("type", "").upper() == "WHATSAPP"]

            if not whatsapp_accounts:
                return {"error": "No WhatsApp account connected"}

            account_id = whatsapp_accounts[0]["id"]

            # Get messages from the specific chat
            messages_result = self.unipile_client.get_messages(account_id, chat_id, limit)

            if "error" in messages_result:
                return {"error": f"Failed to get messages: {messages_result['error']}"}

            # Format messages for better display
            messages = []
            for message in messages_result.get("items", []):
                formatted_message = {
                    "message_id": message.get("id"),
                    "text": message.get("text", ""),
                    "sender_id": message.get("sender_id"),
                    "sender_name": message.get("sender_name") or "Unknown",
                    "message_type": message.get("type", "text"),
                    "timestamp": message.get("created_at"),
                    "is_outgoing": message.get("sender_id") == account_id,
                    "status": message.get("status", "unknown"),
                    "media_url": message.get("media_url"),
                    "media_type": message.get("media_type")
                }
                messages.append(formatted_message)

            # Sort messages by timestamp (oldest first)
            messages.sort(key=lambda x: x.get("timestamp", ""))

            return {
                "success": True,
                "messages": messages,
                "total": len(messages),
                "chat_id": chat_id,
                "account_id": account_id
            }

        except Exception as e:
            self.logger.error(f"Error getting chat messages: {e}")
            return {"error": str(e)}

    def get_chat_history(self, phone_number: str = None, limit: int = 50) -> Dict:
        """Get chat history for a specific phone number or all conversations"""
        if phone_number:
            # Get messages for a specific contact
            conversations = self.get_conversations(limit=100)  # Get more conversations to find the right one

            if "error" in conversations:
                return conversations

            # Find the conversation with this phone number
            target_chat = None
            clean_phone = phone_number.replace("+", "").replace(" ", "").replace("-", "")

            for conv in conversations.get("conversations", []):
                for phone in conv.get("phone_numbers", []):
                    clean_conv_phone = phone.replace("+", "").replace(" ", "").replace("-", "")
                    if clean_conv_phone == clean_phone:
                        target_chat = conv
                        break
                if target_chat:
                    break

            if not target_chat:
                return {"error": f"No conversation found with {phone_number}"}

            # Get messages for this specific chat
            return self.get_chat_messages(target_chat["chat_id"], limit)
        else:
            # Get all conversations
            return self.get_conversations(limit)

# Example usage
if __name__ == "__main__":
    try:
        whatsapp = WhatsAppMessaging()

        # Check connection status
        status = whatsapp.check_connection_status()
        print(f"Connection status: {status}")

        # Test authentication (generates QR code if not connected)
        auth_result = whatsapp.authenticate_account()
        print(f"Authentication result: {auth_result}")

        if auth_result.get("qr_required"):
            print("Please scan the QR code with your WhatsApp mobile app")
            print("QR code data available in auth_result['qr_code']")

        # Example: Send message (uncomment to test)
        # result = whatsapp.send_message("+**********", "Hello from WhatsApp via Unipile!")
        # print(f"Message result: {result}")

        # Example: Send bulk messages (uncomment to test)
        # recipients = [
        #     {"phone_number": "+**********", "name": "John"},
        #     {"phone_number": "+**********", "name": "Jane"}
        # ]
        # bulk_result = whatsapp.send_bulk_messages(recipients, "Hello {name}! This message was sent via Unipile.")
        # print(f"Bulk result: {bulk_result}")

    except Exception as e:
        print(f"Error initializing WhatsApp messaging: {e}")
        print("Make sure Unipile API is configured properly")
